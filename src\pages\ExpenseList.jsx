import React, { useState, useEffect } from 'react';
import { Plus, Edit2, Trash2, Search, DollarSign, Filter, X } from 'lucide-react';
import ModernButton from '../components/ModernButton';
import { getExpenses, saveExpense, updateExpense, deleteExpense } from '../services/storage';

const periodOptions = [
  { value: 'day', label: 'Hari Ini' },
  { value: 'week', label: 'Minggu Ini' },
  { value: 'month', label: 'Bulan Ini' },
  { value: 'year', label: 'Tahun Ini' },
];

const ExpenseList = () => {
  const [selectedPeriod, setSelectedPeriod] = useState('month');
  const [expenses, setExpenses] = useState([]);
  const [search, setSearch] = useState('');
  const [form, setForm] = useState({
    id: null,
    description: '',
    createdat: '',
    amount: ''
  });
  const [isEditing, setIsEditing] = useState(false);
  const [showModal, setShowModal] = useState(false);

  // Helper: parse date string to Date object (supports both date strings and timestamps)
  const parseDate = (dateStr) => {
    if (!dateStr) return null;
    // Handle ISO timestamp format from database
    if (dateStr.includes('T')) {
      return new Date(dateStr);
    }
    // Handle YYYY-MM-DD format
    const [y, m, d] = dateStr.split(/[-\/]/);
    return new Date(Number(y), Number(m) - 1, Number(d));
  };

  // Format price without Rp prefix
  const formatPrice = (amount) => {
    return new Intl.NumberFormat('id-ID').format(amount);
  };

  // Filter expenses by selected period
  function filterExpensesByPeriod(expenses, period) {
    if (!Array.isArray(expenses)) return [];
    const now = new Date();
    let start, end;
    switch (period) {
      case 'day':
        start = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        end = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1);
        break;
      case 'week': {
        const day = now.getDay() || 7;
        start = new Date(now);
        start.setDate(now.getDate() - day + 1);
        start.setHours(0, 0, 0, 0);
        end = new Date(start);
        end.setDate(start.getDate() + 7);
        break;
      }
      case 'month':
        start = new Date(now.getFullYear(), now.getMonth(), 1);
        end = new Date(now.getFullYear(), now.getMonth() + 1, 1);
        break;
      case 'year':
        start = new Date(now.getFullYear(), 0, 1);
        end = new Date(now.getFullYear() + 1, 0, 1);
        break;
      default:
        start = null;
        end = null;
    }
    return expenses.filter(exp => {
      if (!exp.createdat) return false;
      const d = parseDate(exp.createdat);
      if (start && d < start) return false;
      if (end && d >= end) return false;
      return true;
    });
  }

  const filteredExpenses = filterExpensesByPeriod(expenses, selectedPeriod);

  // Calculate total for filtered expenses
  const totalFiltered = filteredExpenses.reduce((sum, exp) => sum + (exp.amount || 0), 0);

  // Helper: get start/end of today, week, month, year
  const now = new Date();
  const todayStart = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  const todayEnd = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1);
  const weekStart = new Date(now);
  weekStart.setDate(now.getDate() - now.getDay()); // Sunday as start
  weekStart.setHours(0, 0, 0, 0);
  const weekEnd = new Date(weekStart);
  weekEnd.setDate(weekStart.getDate() + 7);
  const monthStart = new Date(now.getFullYear(), now.getMonth(), 1);
  const monthEnd = new Date(now.getFullYear(), now.getMonth() + 1, 1);
  const yearStart = new Date(now.getFullYear(), 0, 1);
  const yearEnd = new Date(now.getFullYear() + 1, 0, 1);

  // Calculate totals
  const totalToday = expenses.filter(exp => {
    if (!exp.createdat) return false;
    const d = parseDate(exp.createdat);
    return d >= todayStart && d < todayEnd;
  }).reduce((sum, exp) => sum + (exp.amount || 0), 0);

  const totalWeek = expenses.filter(exp => {
    if (!exp.createdat) return false;
    const d = parseDate(exp.createdat);
    return d >= weekStart && d < weekEnd;
  }).reduce((sum, exp) => sum + (exp.amount || 0), 0);

  const totalMonth = expenses.filter(exp => {
    if (!exp.createdat) return false;
    const d = parseDate(exp.createdat);
    return d >= monthStart && d < monthEnd;
  }).reduce((sum, exp) => sum + (exp.amount || 0), 0);

  const totalYear = expenses.filter(exp => {
    if (!exp.createdat) return false;
    const d = parseDate(exp.createdat);
    return d >= yearStart && d < yearEnd;
  }).reduce((sum, exp) => sum + (exp.amount || 0), 0);

  // Load expenses from Supabase on mount
  useEffect(() => {
    loadExpenses();
  }, []);

  const loadExpenses = async () => {
    try {
      const data = await getExpenses();
      setExpenses(data || []);
    } catch (error) {
      console.error('Error loading expenses from Supabase, falling back to localStorage:', error);
      // Fallback to localStorage
      const stored = localStorage.getItem('expenses_new');
      if (stored) {
        setExpenses(JSON.parse(stored));
      } else {
        setExpenses([]);
      }
    }
  };



  const handleChange = (e) => {
    setForm({ ...form, [e.target.name]: e.target.value });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    // Validate required fields
    if (!form.description || !form.createdat || form.amount === '' || form.amount === null || form.amount === undefined) {
      alert('Mohon lengkapi semua field yang diperlukan');
      return;
    }

    // Validate amount is a positive number
    const amount = Number(form.amount);
    if (isNaN(amount) || amount < 0) {
      alert('Jumlah harus berupa angka positif');
      return;
    }

    try {
      if (isEditing) {
        const updatedExpense = await updateExpense(form.id, {
          description: form.description,
          amount: Number(form.amount),
          createdat: form.createdat
        });
        if (updatedExpense) {
          await loadExpenses(); // Reload data from Supabase
          setIsEditing(false);
        } else {
          alert('Gagal mengupdate pengeluaran');
          return;
        }
      } else {
        try {
          const newExpense = await saveExpense({
            description: form.description,
            amount: Number(form.amount),
            createdat: form.createdat
          });
          if (newExpense) {
            await loadExpenses(); // Reload data from Supabase
          } else {
            alert('Gagal menambah pengeluaran');
            return;
          }
        } catch (error) {
          console.error('Supabase save failed, using localStorage fallback:', error);
          // Fallback to localStorage
          const newExpense = {
            id: Date.now().toString(),
            description: form.description,
            amount: Number(form.amount),
            createdat: form.createdat
          };
          const currentExpenses = JSON.parse(localStorage.getItem('expenses_new') || '[]');
          const updatedExpenses = [...currentExpenses, newExpense];
          localStorage.setItem('expenses_new', JSON.stringify(updatedExpenses));
          setExpenses(updatedExpenses);
        }
      }

      setForm({ id: null, description: '', createdat: '', amount: '' });
      setShowModal(false);
    } catch (error) {
      console.error('Error saving expense:', error);
      alert('Terjadi kesalahan saat menyimpan data: ' + error.message);
    }
  };

  const handleEdit = (expense) => {
    // Convert createdat from database timestamp format to date input format (YYYY-MM-DD)
    const formattedExpense = {
      ...expense,
      createdat: expense.createdat ? new Date(expense.createdat).toISOString().split('T')[0] : ''
    };
    setForm(formattedExpense);
    setIsEditing(true);
    setShowModal(true);
  };

  const handleAddNew = () => {
    setForm({ id: null, description: '', createdat: new Date().toISOString().split('T')[0], amount: '' });
    setIsEditing(false);
    setShowModal(true);
  };

  const handleCloseModal = () => {
    setShowModal(false);
    setIsEditing(false);
    setForm({ id: null, description: '', createdat: '', amount: '' });
  };

  const handleDelete = async (id) => {
    if (window.confirm('Hapus pengeluaran ini?')) {
      try {
        const success = await deleteExpense(id);
        if (success) {
          await loadExpenses(); // Reload data from Supabase
        } else {
          alert('Gagal menghapus pengeluaran');
        }
      } catch (error) {
        console.error('Error deleting expense:', error);
        alert('Terjadi kesalahan saat menghapus data');
      }
    }
  };

  return (
    <div className="w-full max-w-screen-md mx-auto px-2 sm:px-4 space-y-4" style={{ fontFamily: 'Poppins, Nunito, Inter, sans-serif' }}>
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <h1 className="text-2xl sm:text-3xl font-bold mb-2 text-center" style={{ letterSpacing: 0.5, background: 'var(--theme-gradient, linear-gradient(90deg, #4caf50, #22d3ee))', WebkitBackgroundClip: 'text', WebkitTextFillColor: 'transparent', backgroundClip: 'text', color: 'transparent' }}>Pengeluaran</h1>
        <ModernButton
          onClick={handleAddNew}
          color="primary"
          size="large"
          style={{
            borderRadius: 14,
            boxShadow: '0 4px 24px rgba(16,185,129,0.13)',
            fontWeight: 700,
            fontSize: 16,
            padding: '12px 28px',
            minHeight: 48,
            minWidth: 140,
            letterSpacing: 0.5
          }}
          startIcon={<Plus style={{ width: 20, height: 20 }} />}
        >
          Tambah
        </ModernButton>
      </div>

      {/* Period Selector */}
      <div className="flex flex-wrap gap-2 items-center mb-6">
        <label className="font-semibold flex items-center"><Filter size={16} className="inline mr-1" />Periode:</label>
        {periodOptions.map(opt => (
          <button
            key={opt.value}
            className="px-3 py-1 rounded-full font-medium border transition-colors"
            style={{
              backgroundColor: selectedPeriod === opt.value ? 'var(--primary-color, #FEE715)' : 'transparent',
              color: selectedPeriod === opt.value ? 'var(--secondary-color, #101820)' : 'var(--primary-color, #FEE715)',
              borderColor: 'var(--primary-color, #FEE715)'
            }}
            onClick={() => setSelectedPeriod(opt.value)}
          >
            {opt.label}
          </button>
        ))}
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        {[
          { value: 'day', label: 'Hari Ini', total: totalToday },
          { value: 'week', label: 'Minggu Ini', total: totalWeek },
          { value: 'month', label: 'Bulan Ini', total: totalMonth },
          { value: 'year', label: 'Tahun Ini', total: totalYear },
        ].map(opt => {
          const isActive = selectedPeriod === opt.value;
          return (
            <div
              key={opt.value}
              className={`relative overflow-hidden rounded-xl bg-gradient-to-br from-red-50 to-red-100 border border-red-200 hover:border-red-300 transition-all duration-300 hover:shadow-lg cursor-pointer ${isActive ? 'ring-2 ring-red-500 shadow-lg border-red-300' : ''}`}
              style={{ minHeight: 140 }}
              onClick={() => setSelectedPeriod(opt.value)}
            >
              <div className="p-6">
                {/* Header with icon and label */}
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 rounded-lg bg-gradient-to-br from-red-100 to-red-200">
                      <DollarSign className="h-5 w-5 text-red-600" />
                    </div>
                    <div>
                      <h3 className="text-sm font-semibold text-red-700 uppercase tracking-wide">{opt.label}</h3>
                      <p className="text-xs text-red-500 mt-0.5">Total Pengeluaran</p>
                    </div>
                  </div>
                </div>

                {/* Expense amount */}
                <div className="space-y-1">
                  <p className="text-2xl font-bold text-red-800 leading-tight">
                    {opt.total.toLocaleString('id-ID', { style: 'currency', currency: 'IDR' })}
                  </p>
                  <div className="flex items-center space-x-2">
                    <div className="h-1 w-8 bg-gradient-to-r from-red-400 to-red-500 rounded-full"></div>
                    <span className="text-xs text-red-600">Pengeluaran periode ini</span>
                  </div>
                </div>
              </div>

              {/* Active indicator */}
              {isActive && (
                <div className="absolute top-0 right-0 w-0 h-0 border-l-[20px] border-l-transparent border-t-[20px] border-t-red-500"></div>
              )}
            </div>
          );
        })}
      </div>

      {/* Modal */}
      {showModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-xl shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between p-6 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900">
                {isEditing ? 'Edit Pengeluaran' : 'Tambah Pengeluaran'}
              </h3>
              <button
                onClick={handleCloseModal}
                className="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <X className="h-6 w-6" />
              </button>
            </div>
            <form onSubmit={handleSubmit} className="p-6">
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Deskripsi Pengeluaran
                  </label>
                  <input
                    type="text"
                    name="description"
                    value={form.description}
                    onChange={handleChange}
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 outline-none transition-colors"
                    placeholder="Masukkan deskripsi pengeluaran"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Tanggal
                  </label>
                  <input
                    type="date"
                    name="createdat"
                    value={form.createdat}
                    onChange={handleChange}
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 outline-none transition-colors"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Jumlah (Rp)
                  </label>
                  <input
                    type="number"
                    name="amount"
                    value={form.amount}
                    onChange={handleChange}
                    min="0"
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 outline-none transition-colors"
                    placeholder="0"
                  />
                </div>
              </div>
              <div className="flex gap-3 mt-6 pt-4 border-t border-gray-200">
                <ModernButton
                  type="button"
                  color="inherit"
                  onClick={handleCloseModal}
                  style={{
                    flex: 1,
                    borderRadius: 8,
                    fontWeight: 600,
                    padding: '10px 16px'
                  }}
                >
                  Batal
                </ModernButton>
                <ModernButton
                  type="submit"
                  color="primary"
                  style={{
                    flex: 1,
                    borderRadius: 8,
                    fontWeight: 600,
                    padding: '10px 16px'
                  }}
                >
                  {isEditing ? 'Update' : 'Tambah'}
                </ModernButton>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Expense List */}
      <div className="card">
        <div className="card-header bg-light">
          <h5 className="card-title mb-0 fw-semibold text-dark">
            Daftar Pengeluaran ({filteredExpenses.length})
          </h5>
        </div>
        <div className="card-body p-0">
          {filteredExpenses.length > 0 ? (
            <>
              {/* Desktop Table */}
              <div className="d-none d-md-block">
                <div className="table-responsive">
                  <table className="table table-hover mb-0 table-sm">
                    <thead className="table-light">
                      <tr>
                        <th scope="col" className="fw-semibold text-dark border-0 ps-3 py-2">Deskripsi</th>
                        <th scope="col" className="fw-semibold text-dark border-0 py-2">Tanggal</th>
                        <th scope="col" className="fw-semibold text-dark border-0 text-end py-2">Jumlah</th>
                        <th scope="col" className="fw-semibold text-dark border-0 text-center pe-3 py-2">Act</th>
                      </tr>
                    </thead>
                    <tbody>
                      {filteredExpenses.sort((a, b) => parseDate(a.createdat) - parseDate(b.createdat)).map((expense) => (
                        <tr key={expense.id} className="border-light">
                          <td className="ps-3 py-2 align-middle">
                            <span className="fw-medium text-dark text-nowrap">{expense.description}</span>
                          </td>
                          <td className="py-2 align-middle">
                            <span className="text-muted small text-nowrap">{new Date(expense.createdat).toLocaleDateString('id-ID', {
                              day: '2-digit',
                              month: '2-digit',
                              year: '2-digit'
                            })}</span>
                          </td>
                          <td className="py-2 text-end align-middle">
                            <span className="fw-semibold text-danger small text-nowrap">
                              {formatPrice(expense.amount)}
                            </span>
                          </td>
                          <td className="py-2 text-center pe-3 align-middle">
                            <div className="d-flex align-items-center justify-content-center gap-1">
                              <button
                                onClick={() => handleEdit(expense)}
                                className="btn btn-outline-primary btn-sm d-flex align-items-center justify-content-center p-1"
                                style={{ width: '28px', height: '28px' }}
                                title="Edit"
                              >
                                <Edit2 style={{ width: '14px', height: '14px' }} />
                              </button>
                              <button
                                onClick={() => handleDelete(expense.id)}
                                className="btn btn-outline-danger btn-sm d-flex align-items-center justify-content-center p-1"
                                style={{ width: '28px', height: '28px' }}
                                title="Hapus"
                              >
                                <Trash2 style={{ width: '14px', height: '14px' }} />
                              </button>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>

              {/* Mobile Cards */}
              <div className="d-md-none">
                <div className="p-3 bg-light border-bottom">
                  <div className="row">
                    <div className="col-4">
                      <small className="fw-semibold text-muted text-uppercase">Deskripsi</small>
                    </div>
                    <div className="col-3">
                      <small className="fw-semibold text-muted text-uppercase">Tanggal</small>
                    </div>
                    <div className="col-3">
                      <small className="fw-semibold text-muted text-uppercase">Jumlah</small>
                    </div>
                    <div className="col-2">
                      <small className="fw-semibold text-muted text-uppercase">Act</small>
                    </div>
                  </div>
                </div>
                {filteredExpenses.sort((a, b) => parseDate(a.createdat) - parseDate(b.createdat)).map((expense) => (
                  <div key={expense.id} className="border-bottom border-light">
                    <div className="p-3">
                      <div className="row align-items-center">
                        <div className="col-4">
                          <span className="fw-medium text-dark small text-truncate d-block">{expense.description}</span>
                        </div>
                        <div className="col-3">
                          <span className="text-muted small">{new Date(expense.createdat).toLocaleDateString('id-ID', {
                            day: '2-digit',
                            month: '2-digit'
                          })}</span>
                        </div>
                        <div className="col-3">
                          <span className="fw-semibold text-danger small">
                            {formatPrice(expense.amount)}
                          </span>
                        </div>
                        <div className="col-2">
                          <div className="d-flex gap-1 justify-content-center">
                            <button
                              onClick={() => handleEdit(expense)}
                              className="btn btn-outline-primary btn-sm d-flex align-items-center justify-content-center p-1"
                              style={{ width: '22px', height: '22px' }}
                              title="Edit"
                            >
                              <Edit2 style={{ width: '11px', height: '11px' }} />
                            </button>
                            <button
                              onClick={() => handleDelete(expense.id)}
                              className="btn btn-outline-danger btn-sm d-flex align-items-center justify-content-center p-1"
                              style={{ width: '22px', height: '22px' }}
                              title="Hapus"
                            >
                              <Trash2 style={{ width: '11px', height: '11px' }} />
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </>
          ) : (
            <div className="text-center py-5">
              <DollarSign className="mx-auto h-12 w-12 text-gray-400 mb-3" />
              <h5 className="fw-medium text-gray-900 mb-2">Belum ada pengeluaran</h5>
              <p className="text-muted mb-0">
                Mulai dengan menambahkan pengeluaran pertama Anda.
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ExpenseList;
