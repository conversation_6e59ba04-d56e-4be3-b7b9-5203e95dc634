import React, { useState, useEffect } from 'react'
import { Link } from 'react-router-dom'
import { FileText, Eye, Download, Edit, Image, AlertCircle, Trash2 } from 'lucide-react'
import { getInvoices, getCustomers, updateInvoice, deleteInvoice, getProducts, initializeSampleData } from '../services/storage'
import { generatePDF } from '../services/pdfService'
import { generateInvoiceImage } from '../services/imageService'
import CreateInvoiceButton from '../components/CreateInvoiceButton'
import { useGlobalDialog } from '../contexts/DialogContext'
import { getCurrentTheme, themes } from '../services/themeService'
import ModernButton from '../components/ModernButton'

const Invoices = () => {
  const [invoices, setInvoices] = useState([])
  const [customers, setCustomers] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [activeTab, setActiveTab] = useState('unpaid')
  const { deleteConfirm, success, error: showError } = useGlobalDialog()
  const currentTheme = getCurrentTheme()
  const themeColor = themes[currentTheme]?.primary || 'emerald'

  useEffect(() => {
    loadData()
  }, [])

  const loadData = async () => {
    try {
      // Initialize sample data if no data exists
      const existingInvoicesRaw = await getInvoices()
      const existingCustomersRaw = await getCustomers()
      const existingInvoices = Array.isArray(existingInvoicesRaw) ? existingInvoicesRaw : []
      const existingCustomers = Array.isArray(existingCustomersRaw) ? existingCustomersRaw : []

      if (existingInvoices.length === 0 && existingCustomers.length === 0) {
        initializeSampleData()
      }

      const invoiceDataRaw = await getInvoices()
      const customerDataRaw = await getCustomers()
      const invoiceData = Array.isArray(invoiceDataRaw) ? invoiceDataRaw : []
      const customerData = Array.isArray(customerDataRaw) ? customerDataRaw : []

      // Sort items in each invoice by date
      const invoicesWithSortedItems = invoiceData.map(invoice => ({
        ...invoice,
        items: Array.isArray(invoice.items) ? [...invoice.items].sort((a, b) => new Date(a.createdat) - new Date(b.createdat)) : []
      }))

      setInvoices(invoicesWithSortedItems)
      setCustomers(customerData)
      setError(null)
    } catch (err) {
      setError('Terjadi kesalahan saat memuat data')
      console.error('Error loading data:', err)
    } finally {
      setLoading(false)
    }
  }

  const getCustomerName = (customerId) => {
    const customer = customers.find(c => c.id === customerId)
    return customer ? customer.nama : 'Unknown'
  }

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR'
    }).format(amount)
  }

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('id-ID')
  }

  const getStatusColor = (status) => {
    switch (status) {
      case 'unpaid':
        return 'bg-yellow-500'
      case 'paid':
        return 'bg-green-500'
      default:
        return 'bg-yellow-500'
    }
  }

  const getStatusText = (status) => {
    switch (status) {
      case 'unpaid':
        return 'Belum Lunas'
      case 'paid':
        return 'Lunas'
      default:
        return 'Belum Lunas'
    }
  }

  const handleStatusChange = (invoiceId, newStatus) => {
    const updateData = { status: newStatus }

    // Handle paidat field based on status
    if (newStatus === 'paid') {
      updateData.paidat = new Date().toISOString()
    } else {
      updateData.paidat = null
    }

    updateInvoice(invoiceId, updateData)
    loadData()
  }

  const handleDeleteInvoice = async (invoice) => {
    const customerName = getCustomerName(invoice.pelangganid)

    const confirmed = await deleteConfirm({
      title: 'Hapus Invoice',
      message: `Apakah Anda yakin ingin menghapus invoice ${invoice.nomorinvoice} untuk ${customerName}?\n\nTindakan ini tidak dapat dibatalkan.`,
      confirmText: 'Hapus',
      cancelText: 'Batal'
    })

    if (confirmed) {
      try {
        const deleteSuccess = deleteInvoice(invoice.id)
        if (deleteSuccess) {
          await success({
            title: 'Berhasil!',
            message: 'Invoice berhasil dihapus!'
          })
          loadData() // Refresh the invoice list
        } else {
          await showError({
            title: 'Gagal!',
            message: 'Gagal menghapus invoice. Silakan coba lagi.'
          })
        }
      } catch (error) {
        console.error('Error deleting invoice:', error)
        await showError({
          title: 'Error!',
          message: 'Terjadi kesalahan saat menghapus invoice.'
        })
      }
    }
  }

  const handleDownloadPDF = async (invoice) => {
    const customer = customers.find(c => c.id === invoice.pelangganid)
    await generatePDF(invoice, customer)
  }

  const handleDownloadImage = async (invoice) => {
    const customer = customers.find(c => c.id === invoice.pelangganid)
    const tempDiv = document.createElement('div')
    tempDiv.id = 'temp-invoice-content'
    tempDiv.style.position = 'absolute'
    tempDiv.style.left = '-9999px'
    tempDiv.style.width = '800px'
    tempDiv.style.backgroundColor = 'white'
    tempDiv.style.padding = '20px'
    tempDiv.style.fontFamily = 'Inter, system-ui, sans-serif'

    tempDiv.innerHTML = `
      <div style="border: 1px solid #e5e7eb; border-radius: 12px; overflow: hidden; background: white;">
        <!-- Header -->
        <div style="padding: 24px; border-bottom: 1px solid #f3f4f6; background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);">
          <div style="display: flex; justify-content: space-between; align-items: start;">
            <div style="text-align: left;">
              <h1 style="font-size: 28px; font-weight: bold; color: #059669; margin: 0 0 12px 0;">ROTI RAGIL</h1>
              <div style="margin: 8px 0;">
                <p style="font-size: 16px; font-weight: 600; color: #1f2937; background: #fef3c7; padding: 6px 16px; border-radius: 8px; border: 1px solid #f59e0b; display: inline-block;">No. P-IRT: 2053471011676-30</p>
              </div>
              <p style="font-size: 15px; font-weight: 500; color: #374151; margin: 8px 0 0 0; text-align: left;">Telp: 0895402652626</p>
            </div>
            <div style="text-align: right;">
              <h2 style="font-size: 24px; font-weight: bold; color: #374151; margin: 0 0 8px 0;">INVOICE</h2>
              <p style="font-size: 14px; color: #6b7280; margin: 0;">No: ${invoice.nomorinvoice}</p>
              <p style="font-size: 14px; color: #6b7280; margin: 0;">Tanggal: ${formatDate(invoice.createdat)}</p>
            </div>
          </div>
        </div>

        <!-- Customer -->
        <div style="padding: 24px; border-bottom: 1px solid #f3f4f6;">
          <h3 style="font-size: 18px; font-weight: 600, color: #374151; margin: 0 0 12px 0;">Kepada:</h3>
          <div style="background: #f9fafb; padding: 16px; border-radius: 8px;">
            <p style="font-size: 18px; font-weight: 600, color: #374151; margin: 0;">${customer ? customer.nama : 'Unknown'}</p>
          </div>
        </div>

        <!-- Items -->
        <div style="padding: 24px;">
          <table style="width: 100%; border-collapse: collapse;">
            <thead>
              <tr style="background: linear-gradient(135deg, #f9fafb 0%, #f3f4f6 100%);">
                <th style="padding: 12px; text-align: left; font-size: 12px; font-weight: 500; color: #6b7280; text-transform: uppercase;">No</th>
                <th style="padding: 12px; text-align: left; font-size: 12px; font-weight: 500; color: #6b7280; text-transform: uppercase;">Produk</th>
                <th style="padding: 12px; text-align: center; font-size: 12px; font-weight: 500; color: #6b7280; text-transform: uppercase;">Tanggal</th>
                <th style="padding: 12px; text-align: center; font-size: 12px; font-weight: 500; color: #6b7280; text-transform: uppercase;">Qty</th>
                <th style="padding: 12px; text-align: right; font-size: 12px; font-weight: 500; color: #6b7280; text-transform: uppercase;">Harga</th>
                <th style="padding: 12px; text-align: right; font-size: 12px; font-weight: 500; color: #6b7280; text-transform: uppercase;">Total</th>
              </tr>
            </thead>
            <tbody>
              ${invoice.items.map((item, index) => {
                const productsData = getProducts()
                const product = productsData.find(p => p.id === item.produkId)
                const productName = product ? product.nama : 'Produk Tidak Ditemukan'
                return `
                  <tr style="border-bottom: 1px solid #f3f4f6;">
                    <td style="padding: 16px; font-size: 14px; font-weight: 500; color: #374151;">${index + 1}</td>
                    <td style="padding: 16px; font-size: 14px; font-weight: 500; color: #374151;">${productName}</td>
                    <td style="padding: 16px; font-size: 14px; color: #6b7280; text-align: center;">${item.createdat ? new Date(item.createdat).toLocaleDateString('id-ID') : '-'}</td>
                    <td style="padding: 16px; font-size: 14px; font-weight: 500; color: #374151; text-align: center;">${item.quantity}</td>
                    <td style="padding: 16px; font-size: 14px; color: #374151; text-align: right;">${formatCurrency(item.harga)}</td>
                    <td style="padding: 16px; font-size: 14px; font-weight: 600; color: #374151; text-align: right;">${formatCurrency(item.quantity * item.harga)}</td>
                  </tr>
                `
              }).join('')}
            </tbody>
          </table>
        </div>

        <!-- Total -->
        <div style="padding: 24px; border-top: 1px solid #f3f4f6; background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);">
          <div style="display: flex; justify-content: space-between; align-items: center;">
            <div>
              <p style="font-size: 14px; font-weight: 500; color: #374151; margin: 0;">Terima kasih atas kepercayaan Anda!</p>
              <p style="font-size: 14px; color: #6b7280; margin: 0;">Pembayaran dapat dilakukan melalui transfer bank atau tunai.</p>
            </div>
            <div style="text-align: right;">
              <p style="font-size: 14px; color: #6b7280; margin: 0 0 4px 0;">Total Pembayaran</p>
              <p style="font-size: 24px; font-weight: bold; color: #059669; margin: 0;">${formatCurrency(invoice.total)}</p>
            </div>
          </div>
        </div>
      </div>
    `

    document.body.appendChild(tempDiv)

    try {
      await generateInvoiceImage(invoice, customer, 'temp-invoice-content')
    } finally {
      document.body.removeChild(tempDiv)
    }
  }

  const getFilteredInvoices = () => {
    switch (activeTab) {
      case 'unpaid':
        return invoices.filter(invoice => invoice.status === 'unpaid')
      case 'paid':
        return invoices.filter(invoice => invoice.status === 'paid')
      default:
        return invoices
    }
  }

  if (loading) {
    return (
      <div className="h-[80vh] flex flex-col items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-emerald-500 mb-4"></div>
        <p className="text-gray-600 font-medium">Memuat invoice...</p>
      </div>
    )
  }

  if (error) {
    return (
      <div className="h-[60vh] flex flex-col items-center justify-center">
        <div className="bg-red-50 rounded-full p-3 mb-4">
          <AlertCircle className="h-8 w-8 text-red-500" />
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">Gagal memuat data</h3>
        <p className="text-gray-600 mb-4">{error}</p>
        <button
          onClick={loadData}
          className="inline-flex items-center px-4 py-2 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-emerald-600 hover:bg-emerald-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500"
        >
          Coba Lagi
        </button>
      </div>
    )
  }

  const filteredInvoices = getFilteredInvoices()

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="relative overflow-hidden rounded-xl bg-gradient-to-br from-purple-50 to-indigo-100 border border-purple-200 hover:border-purple-300 transition-all duration-300 hover:shadow-lg">
        <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-purple-200/30 to-indigo-300/30 rounded-full -translate-y-16 translate-x-16"></div>
        <div className="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-purple-300/20 to-indigo-200/20 rounded-full translate-y-12 -translate-x-12"></div>

        <div className="relative p-6">
          <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
            <div className="flex items-center space-x-4 flex-1">
              <div className="p-3 rounded-xl bg-gradient-to-br from-purple-100 to-purple-200 shadow-sm">
                <FileText className="h-8 w-8 text-purple-600" />
              </div>
              <div className="flex-1">
                <h1 className="text-2xl font-bold text-gray-900 mb-1">
                  Daftar Invoice
                </h1>
                <p className="text-purple-700 font-medium">
                  Kelola invoice dan transaksi dengan mudah
                </p>
                <div className="flex items-center space-x-2 mt-2">
                  <div className="h-1 w-12 bg-gradient-to-r from-purple-400 to-indigo-500 rounded-full"></div>
                  <span className="text-xs text-purple-600 font-medium">
                    Total: {invoices.length} invoice
                  </span>
                </div>
              </div>
            </div>

            <div className="flex items-center">
              <CreateInvoiceButton />
            </div>
          </div>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="relative overflow-hidden rounded-xl bg-gradient-to-br from-white to-gray-50 border border-gray-200 shadow-sm">
        <div className="p-6">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div className="flex space-x-1 bg-gray-100 rounded-lg p-1">
              <button
                onClick={() => setActiveTab('unpaid')}
                className={`px-4 py-2 rounded-md text-sm font-medium transition-all duration-200 ${
                  activeTab === 'unpaid'
                    ? 'bg-white text-purple-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                Belum Lunas ({invoices.filter(inv => inv.status === 'unpaid').length})
              </button>
              <button
                onClick={() => setActiveTab('paid')}
                className={`px-4 py-2 rounded-md text-sm font-medium transition-all duration-200 ${
                  activeTab === 'paid'
                    ? 'bg-white text-purple-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                Lunas ({invoices.filter(inv => inv.status === 'paid').length})
              </button>
            </div>
            <button
              onClick={loadData}
              className="inline-flex items-center px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg transition-colors duration-200 border border-gray-200 text-sm font-medium"
            >
              <svg className="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              Refresh
            </button>
          </div>
        </div>
      </div>

      {error && (
        <div className="mb-4 p-4 bg-red-50 rounded-lg flex items-center text-red-800 gap-3">
          <AlertCircle className="h-5 w-5" />
          <p>{error}</p>
        </div>
      )}

      {loading ? (
        <div className="flex justify-center items-center py-16">
          <div className="animate-spin rounded-full h-12 w-12 border-4 border-purple-500 border-r-transparent"></div>
        </div>
      ) : filteredInvoices.length > 0 ? (
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-4">
          {filteredInvoices.map((invoice) => (
            <div
              key={invoice.id}
              className="relative overflow-hidden rounded-xl bg-gradient-to-br from-white to-gray-50 border border-gray-200 hover:border-gray-300 transition-all duration-300 hover:shadow-lg group"
            >
              <div className="p-6">
                {/* Invoice Header */}
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center space-x-3 flex-1 min-w-0">
                    <div className="p-2 rounded-lg bg-gradient-to-br from-purple-100 to-purple-200 flex-shrink-0">
                      <FileText className="h-5 w-5 text-purple-600" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <h3 className="text-lg font-semibold text-gray-900 truncate" title={invoice.nomorinvoice}>
                        {invoice.nomorinvoice}
                      </h3>
                      <p className="text-sm text-gray-600 truncate">
                        {getCustomerName(invoice.pelangganid) || 'Pelanggan tidak ditemukan'}
                      </p>
                      <p className="text-xs text-gray-500">
                        {formatDate(invoice.createdat)}
                      </p>
                    </div>
                  </div>
                  {/* Status */}
                  <div className="relative">
                    <span className={`inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium ${
                      invoice.status === 'paid'
                        ? 'bg-green-100 text-green-800'
                        : 'bg-yellow-100 text-yellow-800'
                    }`}>
                      {invoice.status === 'paid' ? 'paid' : 'unpaid'}
                    </span>
                    <select
                      value={invoice.status}
                      onChange={(e) => handleStatusChange(invoice.id, e.target.value)}
                      className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                    >
                      <option value="unpaid">unpaid</option>
                      <option value="paid">paid</option>
                    </select>
                  </div>
                </div>

                {/* Total Amount */}
                <div className="mb-4">
                  <div className="flex items-center space-x-2">
                    <span className="text-xs text-gray-500 uppercase tracking-wide font-medium">Total</span>
                  </div>
                  <p className="text-2xl font-bold text-purple-600 mt-1">
                    {formatCurrency(invoice.total)}
                  </p>
                  <div className="flex items-center space-x-2 mt-2">
                    <div className="h-1 w-8 bg-gradient-to-r from-purple-400 to-indigo-500 rounded-full"></div>
                    <span className="text-xs text-gray-500">Invoice</span>
                  </div>
                </div>

                {/* Actions */}
                <div className="grid grid-cols-2 gap-2">
                  <Link
                    to={`/invoices/${invoice.id}`}
                    className="flex items-center justify-center space-x-1 px-2 py-2 bg-green-50 hover:bg-green-100 text-green-700 rounded-lg transition-colors duration-200 border border-green-200 hover:border-green-300 text-xs font-medium"
                    title="Lihat Detail"
                  >
                    <Eye className="h-3 w-3" />
                    <span>Detail</span>
                  </Link>
                  <Link
                    to={`/invoices/${invoice.id}/edit`}
                    className="flex items-center justify-center space-x-1 px-2 py-2 bg-blue-50 hover:bg-blue-100 text-blue-700 rounded-lg transition-colors duration-200 border border-blue-200 hover:border-blue-300 text-xs font-medium"
                    title="Edit Invoice"
                  >
                    <Edit className="h-3 w-3" />
                    <span>Edit</span>
                  </Link>
                  <button
                    onClick={() => handleDownloadImage(invoice)}
                    className="flex items-center justify-center space-x-1 px-2 py-2 bg-cyan-50 hover:bg-cyan-100 text-cyan-700 rounded-lg transition-colors duration-200 border border-cyan-200 hover:border-cyan-300 text-xs font-medium"
                    title="Download Gambar"
                  >
                    <Image className="h-3 w-3" />
                    <span>Gambar</span>
                  </button>
                  <button
                    onClick={() => handleDownloadPDF(invoice)}
                    className="flex items-center justify-center space-x-1 px-2 py-2 bg-orange-50 hover:bg-orange-100 text-orange-700 rounded-lg transition-colors duration-200 border border-orange-200 hover:border-orange-300 text-xs font-medium"
                    title="Download PDF"
                  >
                    <Download className="h-3 w-3" />
                    <span>PDF</span>
                  </button>
                </div>

                {/* Delete Button - Separate Row */}
                <button
                  onClick={() => handleDeleteInvoice(invoice)}
                  className="w-full mt-2 flex items-center justify-center space-x-1 px-2 py-2 bg-red-50 hover:bg-red-100 text-red-700 rounded-lg transition-colors duration-200 border border-red-200 hover:border-red-300 text-xs font-medium"
                  title="Hapus Invoice"
                >
                  <Trash2 className="h-3 w-3" />
                  <span>Hapus Invoice</span>
                </button>
              </div>

              {/* Hover Effect Indicator */}
              <div className="absolute top-0 right-0 w-0 h-0 border-l-[20px] border-l-transparent border-t-[20px] border-t-purple-500 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center py-16">
          <div className="mx-auto w-20 h-20 bg-gradient-to-br from-gray-100 to-gray-200 rounded-full flex items-center justify-center mb-6">
            <FileText className="h-10 w-10 text-gray-400" />
          </div>
          <h3 className="text-lg font-semibold text-gray-700 uppercase tracking-wide mb-2">
            {activeTab === 'paid' ? 'Belum ada invoice lunas' : 'Belum ada invoice belum lunas'}
          </h3>
          <p className="text-gray-500 mb-8 max-w-md mx-auto">
            Mulai dengan membuat invoice pertama Anda untuk mengelola transaksi penjualan dengan lebih baik.
          </p>
          <CreateInvoiceButton
            variant="primary"
            size="large"
            iconType="create"
            className="w-full sm:w-auto"
          />
        </div>
      )}
    </div>
  )
}

export default Invoices
