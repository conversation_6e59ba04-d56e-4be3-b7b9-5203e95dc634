import React, { useState, useEffect } from 'react'
import { Link } from 'react-router-dom'
import { Package, Users, FileText, TrendingUp, TrendingDown, RefreshCw, Trash } from 'lucide-react'
import ModernButton from '../components/ModernButton'
import CreateInvoiceButton from '../components/CreateInvoiceButton'
import { useGlobalDialog } from '../contexts/DialogContext'
import { getProducts, getCustomers, getInvoices, getExpenses, initializeSampleData, resetToDefaultData, syncDefaultData } from '../services/storage'
import { migrateAllToSupabase } from '../utils/migrateToSupabase'

const Dashboard = () => {
  const [stats, setStats] = useState({
    totalProducts: 0,
    totalCustomers: 0,
    totalInvoices: 0,
    totalRevenue: 0,
    totalExpenses: 0,
    recentInvoices: []
  })
  const { confirm, alert, success, info } = useGlobalDialog()

  useEffect(() => {
    async function loadData() {
      // Remove initializeSampleData() if you want to use only Supabase
      // await initializeSampleData();
      const products = await getProducts();
      const customers = await getCustomers();
      const invoices = await getInvoices();

      // Calculate stats
      const totalRevenueRaw = Array.isArray(invoices) ? invoices.reduce((sum, invoice) => sum + (invoice.total || 0), 0) : 0;

      // Get total pengeluaran (expenses) from Supabase
      let totalPengeluaran = 0;
      try {
        const expenses = await getExpenses();
        totalPengeluaran = Array.isArray(expenses) ? expenses.reduce((sum, exp) => sum + (Number(exp.amount) || 0), 0) : 0;
      } catch (error) {
        console.error('Error loading expenses from Supabase:', error);
        // Fallback to localStorage for backward compatibility
        try {
          const localExpenses = JSON.parse(localStorage.getItem('expenses') || '[]');
          totalPengeluaran = localExpenses.reduce((sum, exp) => sum + (Number(exp.amount) || 0), 0);
        } catch (e) {
          totalPengeluaran = 0;
        }
      }
      const totalRevenue = totalRevenueRaw - totalPengeluaran;
      const recentInvoices = Array.isArray(invoices)
        ? invoices
            .sort((a, b) => new Date(b.createdat) - new Date(a.createdat))
            .slice(0, 5)
            .map(invoice => {
              const customer = customers.find(c => c.id === invoice.pelangganid)
              return {
                ...invoice,
                customerName: customer ? customer.nama : 'Pelanggan Tidak Ditemukan'
              }
            })
        : [];

      setStats({
        totalProducts: Array.isArray(products) ? products.length : 0,
        totalCustomers: Array.isArray(customers) ? customers.length : 0,
        totalInvoices: Array.isArray(invoices) ? invoices.length : 0,
        totalRevenue,
        totalExpenses: totalPengeluaran,
        recentInvoices
      });
    }
    loadData();
  }, [])

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR'
    }).format(amount)
  }

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('id-ID')
  }

  const getStatusColor = (status) => {
    switch (status) {
      case 'unpaid':
        return 'bg-yellow-100 text-yellow-800'
      case 'paid':
        return 'bg-green-100 text-green-800'
      default:
        return 'bg-yellow-100 text-yellow-800'
    }
  }

  const handleResetData = async () => {
    const confirmed = await confirm({
      title: 'Reset Data',
      message: 'Apakah Anda yakin ingin mereset semua data ke data default?\n\nSemua data yang ada akan hilang dan tidak dapat dikembalikan.',
      confirmText: 'Reset',
      cancelText: 'Batal',
      confirmButtonStyle: 'danger'
    })

    if (confirmed) {
      try {
        resetToDefaultData()
        await success({
          title: 'Berhasil!',
          message: 'Data berhasil direset ke data default.'
        })
        window.location.reload() // Refresh page to show new data
      } catch (error) {
        await alert({
          title: 'Error!',
          message: 'Terjadi kesalahan saat mereset data.'
        })
      }
    }
  }

  const handleSyncData = async () => {
    const confirmed = await confirm({
      title: 'Sinkronisasi Data',
      message: 'Sinkronisasi akan menambahkan produk dan pelanggan default terbaru tanpa menghapus data yang sudah ada.\n\nLanjutkan?',
      confirmText: 'Sinkronisasi',
      cancelText: 'Batal'
    })

    if (confirmed) {
      try {
        const result = syncDefaultData()
        if (result.updated) {
          await success({
            title: 'Berhasil!',
            message: result.message
          })
          window.location.reload() // Refresh to show new data
        } else {
          await info({
            title: 'Informasi',
            message: 'Data sudah up to date!'
          })
        }
      } catch (error) {
        await alert({
          title: 'Error!',
          message: 'Terjadi kesalahan saat sinkronisasi data.'
        })
      }
    }
  }

  const handleMigrateData = async () => {
    const result = await migrateAllToSupabase();
    await success({
      title: 'Migrasi Selesai',
      message: `Migrasi data selesai!\n\n${JSON.stringify(result, null, 2)}`
    });
    window.location.reload();
  };

  // Calculate gross revenue (before expenses)
  const grossRevenue = stats.totalRevenue + stats.totalExpenses;

  const statCards = [
    // First row
    {
      title: 'Total Produk',
      subtitle: 'Produk tersedia',
      value: stats.totalProducts,
      icon: Package,
      iconBg: 'bg-gradient-to-br from-blue-100 to-blue-200',
      iconColor: 'text-blue-600',
      progressColor: 'from-blue-400 to-blue-500',
      link: '/products'
    },
    {
      title: 'Total Pelanggan',
      subtitle: 'Pelanggan terdaftar',
      value: stats.totalCustomers,
      icon: Users,
      iconBg: 'bg-gradient-to-br from-green-100 to-emerald-200',
      iconColor: 'text-green-600',
      progressColor: 'from-green-400 to-emerald-500',
      link: '/customers'
    },
    {
      title: 'Total Invoice',
      subtitle: 'Invoice dibuat',
      value: stats.totalInvoices,
      icon: FileText,
      iconBg: 'bg-gradient-to-br from-purple-100 to-purple-200',
      iconColor: 'text-purple-600',
      progressColor: 'from-purple-400 to-purple-500',
      link: '/invoices'
    },
    // Second row
    {
      title: 'Total Revenue',
      subtitle: 'Pendapatan kotor',
      value: formatCurrency(grossRevenue),
      icon: TrendingUp,
      iconBg: 'bg-gradient-to-br from-emerald-100 to-green-200',
      iconColor: 'text-emerald-600',
      progressColor: 'from-emerald-400 to-green-500',
      link: '/reports'
    },
    {
      title: 'Total Expense',
      subtitle: 'Pengeluaran operasional',
      value: formatCurrency(stats.totalExpenses),
      icon: TrendingDown,
      iconBg: 'bg-gradient-to-br from-red-100 to-red-200',
      iconColor: 'text-red-600',
      progressColor: 'from-red-400 to-red-500',
      link: '/expenses'
    },
    {
      title: 'Net Income',
      subtitle: 'Pendapatan bersih',
      value: formatCurrency(stats.totalRevenue),
      icon: TrendingUp,
      iconBg: 'bg-gradient-to-br from-yellow-100 to-amber-200',
      iconColor: 'text-yellow-600',
      progressColor: 'from-yellow-400 to-amber-500',
      link: '/reports'
    }
  ]

  return (
    <div className="space-y-6">
      {/* Welcome Section */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
          <div className="flex-1">
            <h1 className="text-2xl font-bold text-gray-900">Selamat Datang di Roti Ragil</h1>
            <p className="text-gray-600 mt-1">Kelola invoice dan bisnis Anda dengan mudah</p>
          </div>
          <div className="flex items-center space-x-3">
            <ModernButton
              onClick={handleSyncData}
              startIcon={<RefreshCw style={{ fontSize: 18, marginRight: 4 }} />}
              color="primary"
              size="small"
              style={{ fontSize: 14, padding: '6px 16px', minHeight: 32 }}
            >
              Sync Data
            </ModernButton>
            <ModernButton
              onClick={handleResetData}
              startIcon={<Trash style={{ fontSize: 18, marginRight: 4 }} />}
              color="error"
              size="small"
              style={{ fontSize: 14, padding: '6px 16px', minHeight: 32 }}
            >
              Reset Data
            </ModernButton>
            <ModernButton
              onClick={handleMigrateData}
              startIcon={<RefreshCw style={{ fontSize: 18, marginRight: 4 }} />}
              color="secondary"
              size="small"
              style={{ fontSize: 14, padding: '6px 16px', minHeight: 32 }}
            >
              Migrasi Data ke Supabase
            </ModernButton>
            <div className="hidden sm:block">
              <TrendingUp className="h-12 w-12 text-blue-500" />
            </div>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {statCards.map((card, index) => {
          const Icon = card.icon
          return (
            <Link
              key={index}
              to={card.link}
              className="relative overflow-hidden rounded-xl bg-gradient-to-br from-white to-gray-50 border border-gray-200 hover:border-gray-300 transition-all duration-300 hover:shadow-lg"
              style={{ minHeight: 140 }}
            >
              <div className="p-6">
                {/* Header with icon and label */}
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <div className={`p-2 rounded-lg ${card.iconBg}`}>
                      <Icon className={`h-5 w-5 ${card.iconColor}`} />
                    </div>
                    <div>
                      <h3 className="text-sm font-semibold text-gray-700 uppercase tracking-wide">{card.title}</h3>
                      <p className="text-xs text-gray-500 mt-0.5">{card.subtitle}</p>
                    </div>
                  </div>
                </div>

                {/* Value */}
                <div className="space-y-1">
                  <p className="text-2xl font-bold text-gray-900 leading-tight">
                    {card.value}
                  </p>
                  <div className="flex items-center space-x-2">
                    <div className={`h-1 w-8 bg-gradient-to-r ${card.progressColor} rounded-full`}></div>
                    <span className="text-xs text-gray-500">Data terkini</span>
                  </div>
                </div>
              </div>
            </Link>
          )
        })}
      </div>

      {/* Recent Invoices */}
      <div className="relative overflow-hidden rounded-xl bg-gradient-to-br from-white to-gray-50 border border-gray-200 hover:border-gray-300 transition-all duration-300 hover:shadow-lg">
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="p-2 rounded-lg bg-gradient-to-br from-blue-100 to-blue-200">
                <FileText className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <h2 className="text-sm font-semibold text-gray-700 uppercase tracking-wide">Invoice Terbaru</h2>
                <p className="text-xs text-gray-500 mt-0.5">5 invoice terakhir</p>
              </div>
            </div>
            <Link
              to="/invoices"
              className="text-sm text-blue-600 hover:text-blue-500 font-medium transition-colors"
            >
              Lihat Semua
            </Link>
          </div>
        </div>
        <div className="divide-y divide-gray-100">
          {stats.recentInvoices.length > 0 ? (
            stats.recentInvoices.map((invoice) => (
              <div key={invoice.id} className="px-6 py-4 hover:bg-gray-50 transition-colors">
                <div className="flex items-center justify-between">
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-3">
                      <div className="flex-shrink-0">
                        <div className="w-2 h-2 rounded-full bg-gradient-to-r from-blue-400 to-blue-500"></div>
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-semibold text-gray-900 truncate">
                          {invoice.nomorinvoice}
                        </p>
                        <p className="text-sm text-gray-600 truncate">
                          {invoice.customerName}
                        </p>
                        <p className="text-xs text-gray-500">
                          {formatDate(invoice.createdat)}
                        </p>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3 flex-shrink-0">
                    <span className={`inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium transition-colors ${getStatusColor(invoice.status)}`}>
                      {invoice.status === 'paid' ? 'paid' : 'unpaid'}
                    </span>
                    <p className="text-sm font-bold text-gray-900">
                      {formatCurrency(invoice.total || 0)}
                    </p>
                  </div>
                </div>
              </div>
            ))
          ) : (
            <div className="px-6 py-12 text-center">
              <div className="mx-auto w-16 h-16 bg-gradient-to-br from-gray-100 to-gray-200 rounded-full flex items-center justify-center mb-4">
                <FileText className="h-8 w-8 text-gray-400" />
              </div>
              <h3 className="text-sm font-semibold text-gray-700 uppercase tracking-wide">Belum ada invoice</h3>
              <p className="mt-2 text-sm text-gray-500">
                Mulai dengan membuat invoice pertama Anda.
              </p>
              <div className="mt-6">
                <CreateInvoiceButton
                  variant="primary"
                  size="default"
                  iconType="zap"
                />
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default Dashboard
