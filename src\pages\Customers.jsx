import React, { useState, useEffect } from 'react'
import { Plus, Edit, Trash2, Users } from 'lucide-react'
import { getCustomers, saveCustomer, updateCustomer, deleteCustomer } from '../services/storage'
import { useGlobalDialog } from '../contexts/DialogContext'
import ModernButton from '../components/ModernButton'

const Customers = () => {
  const [customers, setCustomers] = useState([])
  const [showModal, setShowModal] = useState(false)
  const [editingCustomer, setEditingCustomer] = useState(null)
  const [formData, setFormData] = useState({
    nama: ''
  })
  const [loading, setLoading] = useState(false)
  const { deleteConfirm, success, error: showError } = useGlobalDialog()

  useEffect(() => {
    loadCustomers()
  }, [])

  const loadCustomers = async () => {
    setLoading(true)
    const data = await getCustomers()
    setCustomers(Array.isArray(data) ? data : [])
    setLoading(false)
  }

  const handleSubmit = async (e) => {
    e.preventDefault()

    // Add default values for removed fields
    const customerData = {
      ...formData,
      alamat: '-',
      telepon: '-',
      email: ''
    }

    try {
      if (editingCustomer) {
        updateCustomer(editingCustomer.id, customerData)
        await success({
          title: 'Berhasil!',
          message: 'Pelanggan berhasil diperbarui!'
        })
      } else {
        saveCustomer(customerData)
        await success({
          title: 'Berhasil!',
          message: 'Pelanggan berhasil ditambahkan!'
        })
      }

      setFormData({ nama: '' })
      setEditingCustomer(null)
      setShowModal(false)
      loadCustomers()
    } catch (error) {
      await showError({
        title: 'Error!',
        message: 'Terjadi kesalahan saat menyimpan pelanggan.'
      })
    }
  }

  const handleEdit = (customer) => {
    setEditingCustomer(customer)
    setFormData({
      nama: customer.nama
    })
    setShowModal(true)
  }

  const handleDelete = async (customer) => {
    const confirmed = await deleteConfirm({
      title: 'Hapus Pelanggan',
      message: `Apakah Anda yakin ingin menghapus pelanggan "${customer.nama}"?\n\nTindakan ini tidak dapat dibatalkan.`,
      confirmText: 'Hapus',
      cancelText: 'Batal'
    })

    if (confirmed) {
      try {
        deleteCustomer(customer.id)
        await success({
          title: 'Berhasil!',
          message: 'Pelanggan berhasil dihapus!'
        })
        loadCustomers()
      } catch (error) {
        await showError({
          title: 'Error!',
          message: 'Terjadi kesalahan saat menghapus pelanggan.'
        })
      }
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="relative overflow-hidden rounded-xl bg-gradient-to-br from-green-50 to-emerald-100 border border-green-200 hover:border-green-300 transition-all duration-300 hover:shadow-lg">
        <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-green-200/30 to-emerald-300/30 rounded-full -translate-y-16 translate-x-16"></div>
        <div className="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-green-300/20 to-emerald-200/20 rounded-full translate-y-12 -translate-x-12"></div>

        <div className="relative p-6">
          <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
            <div className="flex items-center space-x-4 flex-1">
              <div className="p-3 rounded-xl bg-gradient-to-br from-green-100 to-green-200 shadow-sm">
                <Users className="h-8 w-8 text-green-600" />
              </div>
              <div className="flex-1">
                <h1 className="text-2xl font-bold text-gray-900 mb-1">
                  Manajemen Pelanggan
                </h1>
                <p className="text-green-700 font-medium">
                  Kelola data pelanggan dengan mudah
                </p>
                <div className="flex items-center space-x-2 mt-2">
                  <div className="h-1 w-12 bg-gradient-to-r from-green-400 to-emerald-500 rounded-full"></div>
                  <span className="text-xs text-green-600 font-medium">
                    Total: {customers.length} pelanggan
                  </span>
                </div>
              </div>
            </div>

            <div className="flex items-center">
              <ModernButton
                onClick={() => {
                  setEditingCustomer(null)
                  setFormData({ nama: '' })
                  setShowModal(true)
                }}
                startIcon={<Plus className="h-5 w-5" />}
                color="primary"
                style={{
                  fontSize: 14,
                  padding: '10px 20px',
                  borderRadius: '10px',
                  fontWeight: 600
                }}
              >
                Tambah Pelanggan
              </ModernButton>
            </div>
          </div>
        </div>
      </div>

      {/* Customers List */}
      {loading ? (
        <div className="flex justify-center items-center py-16">
          <div className="animate-spin rounded-full h-12 w-12 border-4 border-green-500 border-r-transparent"></div>
        </div>
      ) : customers.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {customers.map((customer) => (
            <div
              key={customer.id}
              className="relative overflow-hidden rounded-xl bg-gradient-to-br from-white to-gray-50 border border-gray-200 hover:border-gray-300 transition-all duration-300 hover:shadow-lg group"
            >
              <div className="p-6">
                {/* Customer Header */}
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center space-x-3 flex-1 min-w-0">
                    <div className="p-2 rounded-lg bg-gradient-to-br from-green-100 to-green-200 flex-shrink-0">
                      <Users className="h-5 w-5 text-green-600" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <h3 className="text-lg font-semibold text-gray-900 truncate" title={customer.nama}>
                        {customer.nama}
                      </h3>
                      <p className="text-sm text-gray-500">Pelanggan</p>
                    </div>
                  </div>
                </div>

                {/* Customer Info */}
                <div className="mb-4">
                  <div className="flex items-center space-x-2">
                    <span className="text-xs text-gray-500 uppercase tracking-wide font-medium">Status</span>
                  </div>
                  <p className="text-lg font-semibold text-green-600 mt-1">
                    Aktif
                  </p>
                  <div className="flex items-center space-x-2 mt-2">
                    <div className="h-1 w-8 bg-gradient-to-r from-green-400 to-emerald-500 rounded-full"></div>
                    <span className="text-xs text-gray-500">Terdaftar</span>
                  </div>
                </div>

                {/* Actions */}
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => handleEdit(customer)}
                    className="flex-1 flex items-center justify-center space-x-2 px-3 py-2 bg-green-50 hover:bg-green-100 text-green-700 rounded-lg transition-colors duration-200 border border-green-200 hover:border-green-300"
                    title="Edit Pelanggan"
                  >
                    <Edit className="h-4 w-4" />
                    <span className="text-sm font-medium">Edit</span>
                  </button>
                  <button
                    onClick={() => handleDelete(customer)}
                    className="flex-1 flex items-center justify-center space-x-2 px-3 py-2 bg-red-50 hover:bg-red-100 text-red-700 rounded-lg transition-colors duration-200 border border-red-200 hover:border-red-300"
                    title="Hapus Pelanggan"
                  >
                    <Trash2 className="h-4 w-4" />
                    <span className="text-sm font-medium">Hapus</span>
                  </button>
                </div>
              </div>

              {/* Hover Effect Indicator */}
              <div className="absolute top-0 right-0 w-0 h-0 border-l-[20px] border-l-transparent border-t-[20px] border-t-green-500 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center py-16">
          <div className="mx-auto w-20 h-20 bg-gradient-to-br from-gray-100 to-gray-200 rounded-full flex items-center justify-center mb-6">
            <Users className="h-10 w-10 text-gray-400" />
          </div>
          <h3 className="text-lg font-semibold text-gray-700 uppercase tracking-wide mb-2">Belum ada pelanggan</h3>
          <p className="text-gray-500 mb-8 max-w-md mx-auto">
            Mulai dengan menambahkan pelanggan pertama Anda untuk memulai bisnis.
          </p>
          <ModernButton
            onClick={() => {
              setEditingCustomer(null)
              setFormData({ nama: '' })
              setShowModal(true)
            }}
            startIcon={<Plus className="h-5 w-5" />}
            color="primary"
            style={{
              fontSize: 14,
              padding: '12px 24px',
              borderRadius: '10px',
              fontWeight: 600
            }}
          >
            Tambah Pelanggan Pertama
          </ModernButton>
        </div>
      )}

      {/* Modal */}
      {showModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm overflow-y-auto h-full w-full z-50 flex items-center justify-center p-4">
          <div className="relative w-full max-w-md mx-auto">
            <div className="relative overflow-hidden rounded-xl bg-gradient-to-br from-white to-gray-50 border border-gray-200 shadow-2xl">
              {/* Header */}
              <div className="px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-green-50 to-emerald-50">
                <div className="flex items-center space-x-3">
                  <div className="p-2 rounded-lg bg-gradient-to-br from-green-100 to-green-200">
                    <Users className="h-5 w-5 text-green-600" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">
                      {editingCustomer ? 'Edit Pelanggan' : 'Tambah Pelanggan'}
                    </h3>
                    <p className="text-sm text-green-600">
                      {editingCustomer ? 'Perbarui informasi pelanggan' : 'Tambahkan pelanggan baru'}
                    </p>
                  </div>
                </div>
              </div>

              {/* Form */}
              <form onSubmit={handleSubmit} className="p-6 space-y-6">
                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-2 uppercase tracking-wide">
                    Nama Pelanggan
                  </label>
                  <input
                    type="text"
                    value={formData.nama}
                    onChange={(e) => setFormData({...formData, nama: e.target.value})}
                    className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-green-400 focus:border-green-400 transition-all duration-200 bg-white"
                    placeholder="Masukkan nama pelanggan"
                    required
                  />
                </div>

                {/* Actions */}
                <div className="flex space-x-3 pt-4">
                  <button
                    type="button"
                    onClick={() => setShowModal(false)}
                    className="flex-1 px-4 py-3 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-xl transition-colors duration-200 border border-gray-200"
                  >
                    Batal
                  </button>
                  <button
                    type="submit"
                    className="flex-1 px-4 py-3 text-sm font-medium text-white bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 rounded-xl transition-all duration-200 shadow-sm"
                  >
                    {editingCustomer ? 'Update Pelanggan' : 'Simpan Pelanggan'}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default Customers
