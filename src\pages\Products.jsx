import React, { useState, useEffect } from 'react'
import { Plus, Edit, Trash2, Package } from 'lucide-react'
import { getProducts, saveProduct, updateProduct, deleteProduct } from '../services/storage'
import { useGlobalDialog } from '../contexts/DialogContext'
import ModernButton from '../components/ModernButton'
import { formatCurrency } from '../utils/formatters'

const Products = () => {
  const [products, setProducts] = useState([])
  const [search, setSearch] = useState("")
  const [showModal, setShowModal] = useState(false)
  const [editingProduct, setEditingProduct] = useState(null)
  const [formData, setFormData] = useState({
    nama: '',
    harga: ''
  })
  const [loading, setLoading] = useState(false)
  const { deleteConfirm, success, error: showError } = useGlobalDialog()

  useEffect(() => {
    loadProducts()
  }, [])

  const loadProducts = async () => {
    setLoading(true)
    const data = await getProducts()
    setProducts(Array.isArray(data) ? data : [])
    setLoading(false)
  }

  const handleSubmit = async (e) => {
    e.preventDefault()

    const productData = {
      nama: formData.nama,
      harga: parseFloat(formData.harga)
    }

    try {
      if (editingProduct) {
        await updateProduct(editingProduct.id, productData)
        await success({
          title: 'Berhasil!',
          message: 'Produk berhasil diperbarui!'
        })
      } else {
        await saveProduct(productData)
        await success({
          title: 'Berhasil!',
          message: 'Produk berhasil ditambahkan!'
        })
      }

      setFormData({ nama: '', harga: '' })
      setEditingProduct(null)
      setShowModal(false)
      loadProducts()
    } catch (error) {
      await showError({
        title: 'Error!',
        message: 'Terjadi kesalahan saat menyimpan produk.'
      })
    }
  }

  const handleEdit = (product) => {
    setEditingProduct(product)
    setFormData({
      nama: product.nama,
      harga: product.harga.toString()
    })
    setShowModal(true)
  }

  const handleDelete = async (product) => {
    const confirmed = await deleteConfirm({
      title: 'Hapus Produk',
      message: `Apakah Anda yakin ingin menghapus produk "${product.nama}"?\n\nTindakan ini tidak dapat dibatalkan.`,
      confirmText: 'Hapus',
      cancelText: 'Batal'
    })

    if (confirmed) {
      try {
        await deleteProduct(product.id)
        await success({
          title: 'Berhasil!',
          message: 'Produk berhasil dihapus!'
        })
        loadProducts()
      } catch (error) {
        await showError({
          title: 'Error!',
          message: 'Terjadi kesalahan saat menghapus produk.'
        })
      }
    }
  }

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR'
    }).format(amount)
  }



  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="relative overflow-hidden rounded-xl bg-gradient-to-br from-emerald-50 to-green-100 border border-emerald-200 hover:border-emerald-300 transition-all duration-300 hover:shadow-lg">
        <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-emerald-200/30 to-green-300/30 rounded-full -translate-y-16 translate-x-16"></div>
        <div className="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-emerald-300/20 to-green-200/20 rounded-full translate-y-12 -translate-x-12"></div>

        <div className="relative p-6">
          <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
            <div className="flex items-center space-x-4 flex-1">
              <div className="p-3 rounded-xl bg-gradient-to-br from-emerald-100 to-emerald-200 shadow-sm">
                <Package className="h-8 w-8 text-emerald-600" />
              </div>
              <div className="flex-1">
                <h1 className="text-2xl font-bold text-gray-900 mb-1">
                  Manajemen Produk
                </h1>
                <p className="text-emerald-700 font-medium">
                  Kelola produk dan harga dengan mudah
                </p>
                <div className="flex items-center space-x-2 mt-2">
                  <div className="h-1 w-12 bg-gradient-to-r from-emerald-400 to-green-500 rounded-full"></div>
                  <span className="text-xs text-emerald-600 font-medium">
                    Total: {products.filter(product => product.nama.toLowerCase().includes(search.toLowerCase())).length} produk
                  </span>
                </div>
              </div>
            </div>

            <div className="flex items-center">
              <ModernButton
                onClick={() => {
                  setEditingProduct(null)
                  setFormData({ nama: '', harga: '' })
                  setShowModal(true)
                }}
                startIcon={<Plus className="h-5 w-5" />}
                color="primary"
                style={{
                  fontSize: 14,
                  padding: '10px 20px',
                  borderRadius: '10px',
                  fontWeight: 600
                }}
              >
                Tambah Produk
              </ModernButton>
            </div>
          </div>
        </div>
      </div>

      {/* Search Bar */}
      <div className="relative">
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <Package className="h-5 w-5 text-gray-400" />
        </div>
        <input
          type="text"
          placeholder="Cari produk berdasarkan nama..."
          value={search}
          onChange={e => setSearch(e.target.value)}
          className="w-full pl-10 pr-4 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-emerald-400 focus:border-emerald-400 transition-all duration-200 bg-white shadow-sm"
          style={{fontSize: 14}}
        />
      </div>
{/* Products List */}
{loading ? (
  <div className="flex justify-center items-center py-16">
    <div className="animate-spin rounded-full h-12 w-12 border-4 border-emerald-500 border-r-transparent"></div>
  </div>
) : products.filter(product => product.nama.toLowerCase().includes(search.toLowerCase())).length > 0 ? (
  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
    {products.filter(product => product.nama.toLowerCase().includes(search.toLowerCase())).map((product) => (
      <div
        key={product.id}
        className="relative overflow-hidden rounded-xl bg-gradient-to-br from-white to-gray-50 border border-gray-200 hover:border-gray-300 transition-all duration-300 hover:shadow-lg group"
      >
        <div className="p-6">
          {/* Product Header */}
          <div className="flex items-start justify-between mb-4">
            <div className="flex items-center space-x-3 flex-1 min-w-0">
              <div className="p-2 rounded-lg bg-gradient-to-br from-emerald-100 to-emerald-200 flex-shrink-0">
                <Package className="h-5 w-5 text-emerald-600" />
              </div>
              <div className="flex-1 min-w-0">
                <h3 className="text-lg font-semibold text-gray-900 truncate" title={product.nama}>
                  {product.nama}
                </h3>
                <p className="text-sm text-gray-500">Produk</p>
              </div>
            </div>
          </div>

          {/* Price */}
          <div className="mb-4">
            <div className="flex items-center space-x-2">
              <span className="text-xs text-gray-500 uppercase tracking-wide font-medium">Harga</span>
            </div>
            <p className="text-2xl font-bold text-emerald-600 mt-1">
              {formatCurrency(product.harga)}
            </p>
            <div className="flex items-center space-x-2 mt-2">
              <div className="h-1 w-8 bg-gradient-to-r from-emerald-400 to-green-500 rounded-full"></div>
              <span className="text-xs text-gray-500">Per unit</span>
            </div>
          </div>

          {/* Actions */}
          <div className="flex items-center space-x-2">
            <button
              onClick={() => handleEdit(product)}
              className="flex-1 flex items-center justify-center space-x-2 px-3 py-2 bg-emerald-50 hover:bg-emerald-100 text-emerald-700 rounded-lg transition-colors duration-200 border border-emerald-200 hover:border-emerald-300"
              title="Edit Produk"
            >
              <Edit className="h-4 w-4" />
              <span className="text-sm font-medium">Edit</span>
            </button>
            <button
              onClick={() => handleDelete(product)}
              className="flex-1 flex items-center justify-center space-x-2 px-3 py-2 bg-red-50 hover:bg-red-100 text-red-700 rounded-lg transition-colors duration-200 border border-red-200 hover:border-red-300"
              title="Hapus Produk"
            >
              <Trash2 className="h-4 w-4" />
              <span className="text-sm font-medium">Hapus</span>
            </button>
          </div>
        </div>

        {/* Hover Effect Indicator */}
        <div className="absolute top-0 right-0 w-0 h-0 border-l-[20px] border-l-transparent border-t-[20px] border-t-emerald-500 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
      </div>
    ))}
  </div>

) : (
  <div className="text-center py-16">
    <div className="mx-auto w-20 h-20 bg-gradient-to-br from-gray-100 to-gray-200 rounded-full flex items-center justify-center mb-6">
      <Package className="h-10 w-10 text-gray-400" />
    </div>
    <h3 className="text-lg font-semibold text-gray-700 uppercase tracking-wide mb-2">Belum ada produk</h3>
    <p className="text-gray-500 mb-8 max-w-md mx-auto">
      Mulai dengan menambahkan produk pertama Anda untuk memulai bisnis.
    </p>
    <ModernButton
      onClick={() => {
        setEditingProduct(null)
        setFormData({ nama: '', harga: '' })
        setShowModal(true)
      }}
      startIcon={<Plus className="h-5 w-5" />}
      color="primary"
      style={{
        fontSize: 14,
        padding: '12px 24px',
        borderRadius: '10px',
        fontWeight: 600
      }}
    >
      Tambah Produk Pertama
    </ModernButton>
  </div>
)}

      {/* Modal */}
      {showModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm overflow-y-auto h-full w-full z-50 flex items-center justify-center p-4">
          <div className="relative w-full max-w-md mx-auto">
            <div className="relative overflow-hidden rounded-xl bg-gradient-to-br from-white to-gray-50 border border-gray-200 shadow-2xl">
              {/* Header */}
              <div className="px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-emerald-50 to-green-50">
                <div className="flex items-center space-x-3">
                  <div className="p-2 rounded-lg bg-gradient-to-br from-emerald-100 to-emerald-200">
                    <Package className="h-5 w-5 text-emerald-600" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">
                      {editingProduct ? 'Edit Produk' : 'Tambah Produk'}
                    </h3>
                    <p className="text-sm text-emerald-600">
                      {editingProduct ? 'Perbarui informasi produk' : 'Tambahkan produk baru'}
                    </p>
                  </div>
                </div>
              </div>

              {/* Form */}
              <form onSubmit={handleSubmit} className="p-6 space-y-6">
                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-2 uppercase tracking-wide">
                    Nama Produk
                  </label>
                  <input
                    type="text"
                    value={formData.nama}
                    onChange={(e) => setFormData({...formData, nama: e.target.value})}
                    className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-emerald-400 focus:border-emerald-400 transition-all duration-200 bg-white"
                    placeholder="Masukkan nama produk"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-semibold text-gray-700 mb-2 uppercase tracking-wide">
                    Harga
                  </label>
                  <div className="relative">
                    <span className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-500 font-medium">Rp</span>
                    <input
                      type="number"
                      value={formData.harga}
                      onChange={(e) => setFormData({...formData, harga: e.target.value})}
                      className="w-full pl-12 pr-4 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-emerald-400 focus:border-emerald-400 transition-all duration-200 bg-white"
                      placeholder="0"
                      required
                    />
                  </div>
                </div>

                {/* Actions */}
                <div className="flex space-x-3 pt-4">
                  <button
                    type="button"
                    onClick={() => setShowModal(false)}
                    className="flex-1 px-4 py-3 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-xl transition-colors duration-200 border border-gray-200"
                  >
                    Batal
                  </button>
                  <button
                    type="submit"
                    className="flex-1 px-4 py-3 text-sm font-medium text-white bg-gradient-to-r from-emerald-500 to-green-600 hover:from-emerald-600 hover:to-green-700 rounded-xl transition-all duration-200 shadow-sm"
                  >
                    {editingProduct ? 'Update Produk' : 'Simpan Produk'}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default Products
